<template>
    <div class="verification-mobile">
        <!-- 未扫码状态 -->
        <div v-if="status === 'NO_ACTION'" class="init verification-step">
            <UrlQrCode v-if="h5FaceCheckUrl" :url="h5FaceCheckUrl" :width="120" :height="120" :options="h5FaceCheckUrlOptios" />
            <div class="desc">
                <p class="title">手机扫码人脸认证</p>
                <p class="guid">推荐使用手机进行认证 成功率更高</p>
            </div>
        </div>
        <!-- 同步方案下，机审没有审核中状态，人工审核有审核中状态 -->
        <!-- 机审&人工 加急审核中 -->
        <div v-else-if="status === 'MANUAL_VERIFYING'" class="verification-step verifying">
            <SvgIcon name="svg-empty-loading-classic" width="18" height="18" class="animate_rotate" />
            <div class="status-text">人工审核中</div>
        </div>
        <!-- 机审&人工 审核结果 -->
        <div v-else-if="status === 'MACHINE_SUCCESS' || status === 'MACHINE_FAIL' || status === 'MANUAL_SUCCESS' || status === 'MANUAL_FAIL'" class="end verification-step">
            <SvgIcon :name="status === 'MACHINE_SUCCESS' || status === 'MANUAL_SUCCESS' ? 'svg-full-face-detect-success' : 'svg-full-face-detect-fail'" width="44" height="44" />
            <div class="status-text">
                {{ computedResText }}
            </div>
            <div v-if="status === 'MANUAL_FAIL'" class="fail-reason" :title="manualAuditResultRemarks">
                {{ manualAuditResultRemarks }}
            </div>
            <b-button v-if="status === 'MACHINE_FAIL' || status === 'MANUAL_FAIL'" type="primary" shape="round" @click="reset"> 重新扫码 </b-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { computed, onMounted, ref } from 'vue';
import { MAX_MACHINE_VERIFY_TIMES } from '../constant';
import UrlQrCode from '@/components/url-qr-code.vue';
import { getH5FaceCheckUrl } from '@/utils/index';
import { useRoute } from 'vue-router';
const route = useRoute();
const { examId } = route.query;

const props = defineProps({
    recognizeFaceFailedCount: {
        type: Number,
        default: 0,
    },
    systemRecognizeResult: {
        type: Number,
        default: 0,
    },
    h5SystemRecognizeResult: {
        type: Number,
        default: 0,
    },
    manualAuditStatus: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResult: {
        type: Number as PropType<number | null>,
        default: null,
    },
    manualAuditResultRemarks: {
        type: String,
        default: '',
    },
});

const h5FaceCheckUrl = ref('');
const h5FaceCheckUrlOptios = {
    errorCorrectionLevel: 'L',
};

const status = ref('');
const computedResText = computed(() => {
    if (status.value === 'MACHINE_SUCCESS') {
        return '手机验证成功';
    }
    if (status.value === 'MACHINE_FAIL') {
        return '手机验证失败';
    }
    if (status.value === 'MANUAL_SUCCESS') {
        return '人工审核通过';
    }
    if (status.value === 'MANUAL_FAIL') {
        return '人工审核失败';
    }
    return '';
});

function changeStatus() {
    // 人工
    if (props.manualAuditStatus) {
        if (props.manualAuditStatus === 1) {
            status.value = 'MANUAL_VERIFYING';
        } else {
            status.value = props.manualAuditResult === 1 ? 'MANUAL_SUCCESS' : 'MANUAL_FAIL';
        }
    } else if (props.recognizeFaceFailedCount < MAX_MACHINE_VERIFY_TIMES) {
        if (props.h5SystemRecognizeResult === 0) {
            status.value = 'NO_ACTION';
        } else if (props.h5SystemRecognizeResult === 1) {
            status.value = 'MACHINE_SUCCESS';
        } else {
            status.value = 'MACHINE_FAIL';
        }
    } else {
        status.value = props.systemRecognizeResult === 1 ? 'MACHINE_SUCCESS' : 'MACHINE_FAIL';
    }
}

onMounted(async () => {
    changeStatus();
    h5FaceCheckUrl.value = await getH5FaceCheckUrl({ examId: examId as string });
});

function reset() {
    status.value = 'NO_ACTION';
}
</script>

<style lang="less" scoped>
.verification-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    line-height: 20px;
    width: 100%;
    height: 100%;
    padding: 0 70px;
    &.init {
        flex-direction: row;
        canvas {
            border-radius: 4px;
            width: 120px;
            height: 120px;
        }
        .desc {
            flex-shrink: 0;
            margin-left: 24px;
            .title {
                font-size: 14px;
                font-weight: 500;
            }
            .guid {
                margin-top: 12px;
                font-size: 12px;
                color: #808080;
            }
        }
    }
    .status-text {
        font-size: 14px;
        font-weight: 500;
        margin-top: 12px;
    }
    &.end {
        svg {
            color: #fff;
        }
        .fail-reason {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            word-wrap: break-word;
            word-break: break-all;
            margin-top: 4px;
            line-height: 17px;
            font-size: 12px;
            color: #939cbc;
            text-align: center;
        }
        .b-button {
            margin-top: 12px;
        }
    }
}
</style>
